import { IBaseApiResponse, TPaginatedData } from "types/apiResponse.type"
import {
  GetMyDisbursementData,
  GetMyDisbursementParams,
} from "types/disbursement.type"

export interface DisbursementRepository {
  getMyDisbursements(
    params: GetMyDisbursementParams,
  ): Promise<TPaginatedData<GetMyDisbursementData>>

  getMyDisbursementById(
    disbursementId: string | number,
  ): Promise<IBaseApiResponse<GetMyDisbursementData>>
}
