/* eslint-disable @typescript-eslint/naming-convention */

import { TApiResponse, TPaginatedData } from "types/apiResponse.type"
import { ELanguage } from "types/misc.type"

// eslint-disable-next-line @typescript-eslint/no-extraneous-class
export class MiscConstant {
  static readonly DATE_FORMAT = "DD MMMM YYYY"
  static readonly DATE_FORMAT_SHORT = "DD MMM, YYYY"
  static readonly DATE_FORMAT_FULL = "yyyy-MM-dd HH:mm:ss"
  static readonly DATE_FORMAT_ISO = "YYYY-MM-DD"
  static readonly DIVIDER_PRICER = 1000
  static readonly PAGING_DEFAULT = {
    PAGE: 0,
    PAGE_SIZE: 10,
    MAX_PAGE_SIZE: 100,
  }

  static readonly FILTER_FIELDS = {
    PAGE: "page",
    PAGE_SIZE: "pageSize",
    TAB: "tab",
    CATEGORY: "category",
    CATEGORY_ID: "categoryId",
    SIZE_ID: "sizeId",
    BRAND_ID: "brandId",
    MAX: "Max",
    MIN: "Min",
    LISTING_PRICE_MIN: "listingPriceMin",
    LISTING_PRICE_MAX: "listingPriceMax",
    SORT_BY: "sortBy",
  }

  static readonly PAGINATED_DATA_DEFAULT: TPaginatedData<any> = {
    page: 0,
    pageSize: 10,
    totalPages: 1,
    content: [],
    first: false,
    last: false,
    sort: [],
    totalSize: 0,
  }

  static readonly OK_RESPONSE_PAGINATED_DATA_DEFAULT: TApiResponse<
    TPaginatedData<any>
  > = {
    code: 200,
    status: "OK",
    message: "OK",
    data: MiscConstant.PAGINATED_DATA_DEFAULT,
  }

  static readonly CONVERT_S3_URL_TO_CLOUD_FRONT = {
    CLOUD_FRONT_DOMAIN: "https://d1ic3o7t1o73go.cloudfront.net",
    S3_BASE_URL:
      "https://kickavenue-media-assets-staging.s3.ap-southeast-1.amazonaws.com",
  }

  static readonly S3_BUCKET_SUFFIX = "kickavenue-media-assets"

  static readonly FOOTER_AUTHENTIC_CONTENT =
    "Kick Avenue, founded in 2017, stands as a premier marketplace for authentic sneakers and collectibles in Indonesia. Boasting a thriving community of over 800,000 users nationwide, our platform has become the go-to destination for enthusiasts seeking genuine products. At Kick Avenue, we prioritize authenticity above all else, ensuring that our users receive exactly what they pay for. With a vast selection of over 100,000 sneakers, apparels, luxury bags, watches, and toys, our marketplace caters to the diverse tastes of our discerning clientele. What sets Kick Avenue apart is our rigorous authentication process. We take the necessary steps to verify the authenticity of every item listed on our platform. Sellers are required to send their products to our facility, where they undergo thorough inspection by our team of experts. Only after meticulous scrutiny and confirmation of authenticity are the goods deemed ready to be shipped to consumers. This commitment to quality assurance instills trust and confidence in our users, ensuring a seamless and satisfying shopping experience every time. As a trusted name in the industry, Kick Avenue has earned a reputation for excellence and reliability. Our dedication to authenticity, coupled with our expansive selection and meticulous verification process, sets us apart as the premier destination for sneakerheads and collectors alike. Join the Kick Avenue community today and experience the thrill of discovering genuine treasures that speak to your unique style and passion."

  static readonly MULTIPART_FORM_DATA = {
    "Content-Type": "multipart/form-data",
  }

  static readonly X_PLATFORM = {
    "X-Platform": "console",
  }

  static readonly OTP_INPUT_TYPE = {
    EMAIL: "EMAIL",
    SMS: "SMS",
  }

  static readonly OTP_INPUT_LENGTH = 6
  static readonly OTP_RESEND_TIME = 58
  static readonly SUCCESS_OTP_MESSAGE = "OTP sent successfully"

  static readonly LANG_CODE_MAP = {
    [ELanguage.Indonesia]: "id",
    [ELanguage.English]: "en",
  }

  static readonly CATEGORY_TYPE = {
    SNEAKERS: "sneakers",
    APPAREL: "apparel",
    TOYS: "toys",
    WATCHES: "watches",
    BAGS: "bags",
  }

  static readonly SORT_BY_OPTIONS = {
    FEATURED_ITEMS: {
      value: "FEATURED_ITEMS",
      text: "Featured Items",
    },
    MOST_POPULAR: {
      value: "MOST_POPULAR",
      text: "Most Popular",
    },
    NEWLY_ADDED: {
      value: "NEWLY_ADDED",
      text: "Newly Added",
    },
    SIZE_AVAILABILITY: {
      value: "SIZE_AVAILABILITY",
      text: "Size Availability",
    },
    PRICE_LOW_TO_HIGH: {
      value: "PRICE_LOW_TO_HIGH",
      text: "Price: Low to High",
    },
    PRICE_HIGH_TO_LOW: {
      value: "PRICE_HIGH_TO_LOW",
      text: "Price: High to Low",
    },
    NAME_A_TO_Z: {
      value: "NAME_A_TO_Z",
      text: "Name: A to Z",
    },
    NAME_Z_TO_A: {
      value: "NAME_Z_TO_A",
      text: "Name: Z to A",
    },
    RECENTLY_ADDED: {
      value: "RECENTLY_ADDED",
      text: "Recently Added",
    },
  }

  static readonly CHECKOUT_PREVIEW_STORAGE_KEY = "checkoutData"

  static readonly SUMMARY_VOUCHER_TITLE = "Voucher"
  static readonly SUMMARY_SELLER_CREDIT_TITLE = "Seller Credit"
  static readonly SUMMARY_KICK_POINTS_TITLE = "Kick Points Usage"
  static readonly SUMMARY_KICK_CREDIT_TITLE = "Kick Credit"
  static readonly SUMMARY_CREDIT_USAGE_TITLE = "Credit Usage"
  static readonly SUMMARY_SHIPPING_FEE_TITLE = "Shipping Fee"
  static readonly SUMMARY_PROCESSING_FEE_TITLE = "Processing Fee"
  static readonly SUMMARY_PRODUCT_PRICE_TITLE = "Product Price"
  static readonly CHECKOUT_URL_SEARCH_PARAM = "durl"
  static readonly TOP_UP_FEE_ID = 1060

  static readonly WINDOW_SIZE = {
    XS: "xs",
    SM: "sm",
    MD: "md",
    LG: "lg",
    XL: "xl",
  }

  static readonly PAYMENT_DEADLINE_IN_DAYS = 1

  static readonly DEFAULT_IDR_ZERO = "IDR 0"

  static readonly BULLET_MASK = "••••••••••"
}
