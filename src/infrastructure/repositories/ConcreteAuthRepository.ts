/* eslint-disable @typescript-eslint/naming-convention */

// src/infrastructure/repositories/ConcreteAuthRepository.ts
import { AuthRepository } from "@application/repositories/AuthRepository"
import { User } from "@domain/entities/User"

export class ConcreteAuthRepository implements AuthRepository {
  async login(email: string, password: string): Promise<User> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/login`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      },
    )

    if (!response.ok) {
      throw new Error("Login failed")
    }

    const userData = await response.json()
    return userData as User
  }

  async logout(): Promise<void> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/logout`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      },
    )

    if (!response.ok) {
      throw new Error("Logout failed")
    }
  }

  async refreshToken(refreshToken: string): Promise<User> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/refresh`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          refresh_token: refreshToken,
        }),
      },
    )

    if (!response.ok) {
      throw new Error("Token refresh failed")
    }

    const userData = await response.json()
    return userData as User
  }

  async resetPassword(
    email: string,
    password: string,
    passwordConfirmation: string,
    token: string,
  ): Promise<void> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/forget-password/password-update`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email,
          password,
          password_confirmation: passwordConfirmation,
          token,
        }),
      },
    )

    if (!response.ok) {
      throw new Error("Failed to reset password")
    }
  }

  async requestPasswordReset(email: string): Promise<void> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/forget-password/reset`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email }),
      },
    )

    if (!response.ok) {
      throw new Error("Failed to request password reset")
    }
  }

  async verifyResetToken(email: string, token: string): Promise<void> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/forget-password/verify?email=${email}&token=${token}`,
      {
        method: "GET",
      },
    )

    if (response.status === 401) {
      throw new Error("Token is invalid")
    }

    if (!response.ok) {
      throw new Error("Failed to verify reset token")
    }
  }
}
