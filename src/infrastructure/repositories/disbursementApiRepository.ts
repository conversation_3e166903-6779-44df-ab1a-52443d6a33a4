import { DisbursementRepository } from "@domain/interfaces/DisbursementRepository"
import { disbursementApi } from "@infrastructure/api/disbursementApi"
import { IBaseApiResponse, TPaginatedData } from "types/apiResponse.type"
import {
  GetMyDisbursementData,
  GetMyDisbursementParams,
} from "types/disbursement.type"

export class DisbursementApiRepository implements DisbursementRepository {
  async getMyDisbursements(
    params: GetMyDisbursementParams,
  ): Promise<TPaginatedData<GetMyDisbursementData>> {
    const res = await disbursementApi.getMyDisbursements(params)
    return Promise.resolve(res?.data as TPaginatedData<GetMyDisbursementData>)
  }

  async getMyDisbursementById(
    disbursementId: string | number,
  ): Promise<IBaseApiResponse<GetMyDisbursementData>> {
    const res = await disbursementApi.getMyDisbursementById(disbursementId)
    return Promise.resolve(res as IBaseApiResponse<GetMyDisbursementData>)
  }
}
