import { MenuWizardRepository } from "@domain/interfaces/MenuWizardRepository"
import { menuWizardApi } from "@infrastructure/api/menuWizardApi"
import { TApiResponse } from "types/apiResponse.type"
import { IMenuWizard } from "types/menuWizard"
import { ELocationPageType } from "types/misc.type"

export class MenuWizardApiRepository implements MenuWizardRepository {
  async getCurrent(
    pageType: ELocationPageType,
  ): Promise<TApiResponse<IMenuWizard>> {
    const res = await menuWizardApi.getCurrent(pageType)
    return Promise.resolve(res as TApiResponse<IMenuWizard>)
  }
}
