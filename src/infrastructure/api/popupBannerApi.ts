import { createBaseHttpClient } from "@infrastructure/providers/httpClient"
import { convertToCamelCase, convertToSnakeCase } from "@utils/misc"
import { IBasePaginatedApiResponse } from "types/apiResponse.type"
import { IPopupBannerData, IPopupBannerParams } from "types/popupBanner.type"

export const popupBannerApi = {
  getCurrent: async (
    params: Partial<IPopupBannerParams>,
  ): Promise<IBasePaginatedApiResponse<IPopupBannerData>> => {
    const client = createBaseHttpClient({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_CONSOLE_URL,
      useToken: false,
    })
    const response = await client.get(`/popup-banner/get-current`, {
      params: convertToSnakeCase(params),
      paramsSerializer: {
        indexes: null,
      },
    })
    return convertToCamelCase(
      response.data,
    ) as IBasePaginatedApiResponse<IPopupBannerData>
  },
}
