import { createBaseHttpClient } from "@infrastructure/providers/httpClient"
import { convertToCamelCase } from "@utils/misc"
import { TApiResponse } from "types/apiResponse.type"
import { IMenuWizard } from "types/menuWizard"
import { ELocationPageType } from "types/misc.type"

export const menuWizardApi = {
  getCurrent: async (
    pageType: ELocationPageType,
  ): Promise<TApiResponse<IMenuWizard>> => {
    const client = createBaseHttpClient({
      baseURL: process.env.NEXT_PUBLIC_API_BASE_CONSOLE_URL,
      useToken: false,
    })
    const response = await client.get(`/menu-wizard/get-current`, {
      params: { pageType },
    })
    return convertToCamelCase(response.data) as TApiResponse<IMenuWizard>
  },
}
