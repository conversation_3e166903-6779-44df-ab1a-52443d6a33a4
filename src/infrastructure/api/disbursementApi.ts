import { convertToCamelCase, convertToSnakeCase } from "@utils/misc"
import { IBaseApiResponse, TApiResponse } from "types/apiResponse.type"
import {
  GetMyDisbursementData,
  GetMyDisbursementParams,
} from "types/disbursement.type"
import { createBaseHttpClient } from "@infrastructure/providers/httpClient"

export const disbursementApi = {
  getMyDisbursements: async (
    params: GetMyDisbursementParams,
  ): Promise<TApiResponse<GetMyDisbursementData>> => {
    const httpClient = createBaseHttpClient({
      useToken: true,
    })

    const response = await httpClient.get(
      `/transaction/payment/disbursement/get/my`,
      {
        params: convertToSnakeCase(params),
        paramsSerializer: {
          indexes: null,
        },
      },
    )

    return convertToCamelCase(
      response.data,
    ) as TApiResponse<GetMyDisbursementData>
  },
  getMyDisbursementById: async (
    disbursementId: string | number,
  ): Promise<IBaseApiResponse<GetMyDisbursementData>> => {
    const httpClient = createBaseHttpClient({
      useToken: true,
    })

    const response = await httpClient.get(
      `/transaction/payment/disbursement/get/my/${disbursementId}`,
    )

    return convertToCamelCase(
      response.data,
    ) as IBaseApiResponse<GetMyDisbursementData>
  },
}
