/* eslint-disable @typescript-eslint/naming-convention */

import { useCallback, useEffect, useRef, useState } from "react"

import { UseOtpTimerResult } from "./types"

// Constants
const DEFAULT_TIMER_DURATION = 180 // 3 minutes in seconds
const TIMER_INTERVAL = 1000 // 1 second

const handleResendRequestLogic = async (to: string, type: string) => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/otp/resend`,
    {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ to, type }),
    },
  )

  if (!response.ok) {
    throw new Error("Failed to resend OTP")
  }
}

export const useOtpTimer = (
  initialDuration: number = DEFAULT_TIMER_DURATION,
): UseOtpTimerResult => {
  const [remainingTime, setRemainingTime] = useState(initialDuration)
  const [canResend, setCanResend] = useState(false)
  const intervalRef = useRef<NodeJS.Timeout | null>(null)

  // Clear any existing interval
  const clearTimer = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current)
      intervalRef.current = null
    }
  }, [])

  // Start countdown timer
  const startCountdown = useCallback(() => {
    clearTimer()

    intervalRef.current = setInterval(() => {
      setRemainingTime((prevTime) => {
        if (prevTime <= 1) {
          clearInterval(intervalRef.current!)
          intervalRef.current = null
          setCanResend(true)
          return 0
        }
        return prevTime - 1
      })
    }, TIMER_INTERVAL)
  }, [clearTimer])

  // Reset timer to initial state
  const resetTimer = useCallback(() => {
    clearTimer()
    setRemainingTime(initialDuration)
    setCanResend(false)
    startCountdown()
  }, [clearTimer, initialDuration, startCountdown])

  // Handle OTP resend request
  const handleResendRequest = useCallback(
    async (to: string, type: string, onError: (error: string) => void) => {
      if (!canResend) {
        onError(
          `Please wait ${remainingTime} seconds before requesting a new code`,
        )
        return
      }

      try {
        await handleResendRequestLogic(to, type)
        resetTimer()
      } catch (err) {
        onError(
          err instanceof Error
            ? err.message
            : "An error occurred while resending OTP",
        )
      }
    },
    [canResend, remainingTime, resetTimer],
  )

  // Initialize timer on mount and cleanup on unmount
  useEffect(() => {
    resetTimer()

    // Cleanup interval on unmount
    return clearTimer
  }, [resetTimer, clearTimer])

  return {
    remainingTime,
    startCountdown,
    handleResendRequest,
    canResend,
  }
}
