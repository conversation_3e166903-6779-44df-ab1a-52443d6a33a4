import { cx } from "class-variance-authority"
import Image from "next/image"
import { useState } from "react"

import styles from "./ProductImage.module.css"
import { ProductImageProps } from "./ProductImage.type"
import Watermark from "@kickavenue/ui/components/Watermark"

const ProductImage = ({ containerProps, imageProps }: ProductImageProps) => {
  const { className, ...rest } = containerProps || {}
  const { className: imageClassName, alt, ...restImageProps } = imageProps

  const [isError, setIsError] = useState(false)

  return (
    <div className={cx(styles["image-container"], className)} {...rest}>
      {!isError && (
        <Image
          alt={alt}
          className={cx(styles.image, imageClassName)}
          onError={() => setIsError(true)}
          {...restImageProps}
        />
      )}
      {isError && (
        <div className="flex !size-full items-center justify-center">
          <Watermark className="size-full" logoClassName="!bg-gray-w-95" />
        </div>
      )}
    </div>
  )
}

export default ProductImage
