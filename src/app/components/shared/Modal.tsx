import { HTMLAttributes, ReactNode, useEffect } from "react"
import { cx } from "class-variance-authority"

import styles from "@app/components/shared/Modal.module.scss"
import { useModalStore } from "stores/modalStore"
import useClickOutside from "@app/hooks/useClickOutside"
import useEscapeClick from "@app/hooks/useEscapeClick"

export interface ModalProps extends HTMLAttributes<HTMLDivElement> {
  children: ReactNode
  modalId?: string
  onClose?: () => void
}

const Modal = (props: ModalProps) => {
  const { children, modalId = "", className } = props
  const { open, setOpen, modalId: currentModalId } = useModalStore()
  const isOpen = open && modalId === currentModalId
  const modalRef = useClickOutside<HTMLDivElement>(isOpen, () => {
    setOpen(false, currentModalId, null)
    props.onClose?.()
  })
  useEscapeClick(() => {
    setOpen(false, currentModalId, null)
    props.onClose?.()
  })
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden"
    }
    return () => {
      document.body.style.overflowY = "auto"
    }
  }, [isOpen])
  return (
    <div
      className={styles.modal}
      aria-labelledby="modal-title"
      role="dialog"
      aria-modal="true"
      data-open={isOpen}
    >
      <div
        className="fixed inset-0 bg-black-dim-60 transition-opacity"
        aria-hidden="true"
      />
      <div className="fixed inset-0 z-10 w-full overflow-y-auto">
        <div className="flex min-h-full items-center justify-center p-4 text-center sm:items-center lg:p-0">
          <div
            ref={modalRef}
            className={cx(
              "shadow-xl relative overflow-hidden rounded-lg bg-white text-left transition-all sm:my-8 sm:w-full sm:max-w-[612px]",
              "[&_div[aria-hidden=true]]:!opacity-0",
              className,
            )}
          >
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

export default Modal
