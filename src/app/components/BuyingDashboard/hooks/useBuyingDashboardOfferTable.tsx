/* eslint-disable max-lines-per-function */

import CheckboxHeader from "@components/SellingDashboard/CheckboxHeader"
import ConditionColumn from "@components/shared/ConditionColumn"
import TitleSubTitleColumn from "@components/shared/Table/TitleSubTitleColumn"
import { formatCurrencyStripe, formatDateObj } from "@utils/misc"
import { useBuyingOfferStore } from "stores/buyingOfferStore"
import { TItemConditionEnum, TPackagingConditionEnum } from "types/listing.type"
import { TOfferV2 } from "types/offer.type"
import { TTableColumn } from "types/table.type"

import BuyingDashboardOfferRowActions from "../BuyingDashboardOffer/BuyingDashboardOfferRowActions"
import BuyingDashboardOfferStatus from "../BuyingDashboardOffer/BuyingDashboardOfferStatus"
import BuyingDashboardProductDetailColumn from "../BuyingDashboardOffer/BuyingDashboardProductDetailColumn"

const columnWidth = {
  checkbox: 44,
  productDetails: 300,
  size: 64,
  conditions: 108,
  offerPrice: 148,
  lowestAsk: 148,
  highestOffer: 148,
  expiryDate: 140,
  paymentMethod: 148,
  status: 128,
  actions: 67,
}

const useBuyingDashboardOfferTable = () => {
  const { selectedRowKeys, setSelectedRowKeys, buyingOfferData } =
    useBuyingOfferStore()

  const columns = [
    {
      key: "checkbox",
      title: "",
      width: columnWidth.checkbox,
      renderHeader: () => (
        <CheckboxHeader
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          data={buyingOfferData}
        />
      ),
    },
    {
      key: "productDetails",
      title: "Product Details",
      width: columnWidth.productDetails,
      render: (record: TOfferV2) => {
        return <BuyingDashboardProductDetailColumn offer={record} />
      },
    },
    {
      key: "size",
      dataIndex: "size",
      title: "Size",
      width: columnWidth.size,
      render: (record: TOfferV2) => (
        <TitleSubTitleColumn
          title="US"
          subtitle={record?.item?.size?.us || "-"}
        />
      ),
    },
    {
      key: "itemCondition",
      title: "Conditions",
      width: columnWidth.conditions,
      render: (record: TOfferV2) => (
        <ConditionColumn
          itemCondition={record?.itemCondition as TItemConditionEnum}
          packagingCondition={record?.boxCondition as TPackagingConditionEnum}
        />
      ),
    },
    {
      key: "amount",
      title: "Offer Price",
      width: columnWidth.offerPrice,
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.amount }),
      sorter: () => {},
    },
    {
      key: "lowestAsk",
      title: "Lowest Ask",
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      width: columnWidth.lowestAsk,
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.lowestAsk }),
    },
    {
      key: "highestOffer",
      title: "Highest Offer",
      headerClassName: "text-right [&>div]:!justify-end",
      contentClassName: "text-right",
      width: columnWidth.highestOffer,
      render: (record: TOfferV2) =>
        formatCurrencyStripe({ price: record?.highestOffer }),
    },
    {
      key: "expiredAt",
      title: "Expiry Date",
      width: columnWidth.expiryDate,
      headerClassName: "text-left [&>div]:!justify-start",
      contentClassName: "!text-left",
      render: (offer: TOfferV2) =>
        offer?.expiredAt ? formatDateObj(new Date(offer.expiredAt)) : "-",
      sorter: () => {},
    },
    {
      key: "status",
      title: "Status",
      width: columnWidth.status,
      render: (offer: TOfferV2) => (
        <BuyingDashboardOfferStatus record={offer} />
      ),
    },
    {
      key: "actions",
      title: "Actions",
      headerClassName: "[&>div]:justify-center",
      className: "[&>div]:justify-center",
      width: columnWidth.actions,
      render: (record: TOfferV2) => (
        <BuyingDashboardOfferRowActions record={record} />
      ),
    },
  ] as TTableColumn[]

  return { columns }
}

export default useBuyingDashboardOfferTable
