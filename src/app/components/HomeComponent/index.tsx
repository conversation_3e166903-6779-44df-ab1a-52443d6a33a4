"use client"

import { snakeCase } from "lodash"
import { useSession } from "next-auth/react"
import { useEffect } from "react"

import useFetchMyWishlist from "@components/Wishlist/hooks/useFetchMyWishlist"
import { event } from "@lib/gtag"
import { useMemberStore } from "stores/memberStore"
import { useSearchStore } from "stores/searchStore"
import { EMenuWizardContentType, IMenuWizardSection } from "types/menuWizard"
import { allRaffleTestData } from "@components/HomeComponent/data/dummyRaffleData"
import { ELocationPageType } from "types/misc.type"

import Banner from "./components/Banner"
import Collection from "./components/Collection"
import Raffle from "./components/Raffle"
import useMenuWizard from "./hooks/useMenuWizard"
import ShortcutMenu from "./components/ShortcutMenu"
import PopupBanner from "./components/PopupBanner"

const HomeComponent = () => {
  const { data: menuWizard } = useMenuWizard(ELocationPageType.HomePage)

  const { status } = useSession()
  const { setProductWishlist } = useSearchStore()
  const { member } = useMemberStore()

  const { data: wishlistData } = useFetchMyWishlist(
    undefined,
    status === "authenticated",
  )

  useEffect(() => {
    if (wishlistData?.items) {
      wishlistData.items.forEach((item: any) => {
        if (item.id && item.itemId) {
          setProductWishlist(item.itemId, item.id)
        }
      })
    }
  }, [wishlistData, setProductWishlist])

  const renderSection = (section: IMenuWizardSection) => {
    switch (section.contentType) {
      case EMenuWizardContentType.Banner:
        return <Banner key={section.id} data={section} />
      case EMenuWizardContentType.Raffle:
        return <Raffle key={section.id} data={[section]} />
      case EMenuWizardContentType.Collection:
        return <Collection key={section.id} data={section} />
      default:
        return null
    }
  }

  useEffect(() => {
    event({
      action: "page_viewed",
      params: {
        [snakeCase("user_id")]: member?.id || "",
        email: member?.email || "",
        [snakeCase("page_title")]: menuWizard?.title || "Home",
        [snakeCase("page_url")]: window.location.href,
      },
    })
  }, [member?.email, member?.id, menuWizard?.title])

  return (
    <>
      {menuWizard?.menuWizardSections.map((section) => renderSection(section))}
      <ShortcutMenu />
      <Raffle data={allRaffleTestData} />

      <PopupBanner />
    </>
  )
}

export default HomeComponent
