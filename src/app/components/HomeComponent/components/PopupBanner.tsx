/* eslint-disable max-lines-per-function */

"use client"

import { IconCloseOutline } from "@kickavenue/ui/components/icons"
import { cx } from "class-variance-authority"
import Image from "next/image"
import { useCallback, useEffect, useState } from "react"

import useGetCurrentPopupBanner from "@app/hooks/useGetCurrentPopupBanner"
import Modal from "@components/shared/Modal"
import Spinner from "@components/shared/Spinner"
import { ModalConstant } from "@constants/modal"
import { convertS3UrlToCloudFront } from "@utils/misc"
import { useModalStore } from "stores/modalStore"
import Watermark from "@kickavenue/ui/components/Watermark"

const { POPUP_BANNER } = ModalConstant.MODAL_IDS

const PopupBanner = () => {
  const { setOpen } = useModalStore()
  const { data } = useGetCurrentPopupBanner()
  const popupBanner = data?.content?.[0]

  const [showContent, setShowContent] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isImageLoaded, setIsImageLoaded] = useState(false)

  const handleClose = () => {
    setShowContent(false)
    setTimeout(() => {
      setOpen(false, POPUP_BANNER)
    }, 200)
  }

  const handleOpen = useCallback(() => {
    setOpen(true, POPUP_BANNER)
    setTimeout(() => setShowContent(true), 50)
  }, [setOpen])

  useEffect(() => {
    if (popupBanner) {
      handleOpen()
    }
  }, [handleOpen, popupBanner])

  if (!popupBanner) return null

  return (
    <Modal
      modalId={POPUP_BANNER}
      onClose={handleClose}
      className={cx(
        "!m-0 !h-[calc(100vh-32px)] w-full !bg-transparent lg:!py-lg",
        "sm:!max-w-[calc(1000px)]",
      )}
    >
      <div className="flex h-full items-center justify-center px-8 lg:px-0">
        <div
          className={cx(
            `relative h-fit w-full transform transition-all duration-300 ease-out`,
            showContent ? "scale-100 opacity-100" : "scale-90 opacity-0",
            "max-w-[calc(100vw-24px)] !rounded-lg sm:max-w-[824px]",
          )}
        >
          {/* landscape image */}
          <Image
            src={convertS3UrlToCloudFront(
              popupBanner.imageLandscape || popupBanner.image,
            )}
            alt={popupBanner.title}
            width={0}
            height={0}
            sizes="(max-width: 640px) 100vw, 824px"
            className={cx(
              "hidden h-auto w-full !rounded-lg object-cover sm:block",
              isError && "hidden",
            )}
            onError={() => setIsError(true)}
            onLoad={() => setIsImageLoaded(false)}
            onLoadingComplete={() => setIsImageLoaded(true)}
          />

          {/* portrait image */}
          <Image
            src={convertS3UrlToCloudFront(popupBanner.imagePortrait || "")}
            alt={popupBanner.title}
            width={0}
            height={0}
            sizes="(max-width: 640px) 100vw, 824px"
            className={cx(
              "block h-auto w-full !rounded-lg object-cover sm:hidden",
              isError && "hidden",
            )}
            onError={() => setIsError(true)}
            onLoad={() => setIsImageLoaded(false)}
            onLoadingComplete={() => setIsImageLoaded(true)}
          />

          {/* loading state */}
          {!isImageLoaded && (
            <div className="flex size-full min-h-[30vh] items-center justify-center">
              <Spinner />
            </div>
          )}

          {/* error image as placeholder image */}
          {isError && (
            <div
              className={cx(
                "flex !size-full items-center justify-center",
                "rounded-lg bg-gray-w-95 py-20 sm:hidden",
              )}
            >
              <Watermark
                className="size-full py-10 sm:py-20"
                logoClassName="!bg-gray-w-95"
              />
            </div>
          )}

          {/* close button */}
          <button
            className="shadow-md absolute -right-8 -top-8 cursor-pointer rounded-full bg-white p-xs"
            type="button"
            onClick={handleClose}
          >
            <IconCloseOutline className="sm:scale-125" />
          </button>
        </div>
      </div>
    </Modal>
  )
}

export default PopupBanner
