import { TList<PERSON>ara<PERSON>, TSort } from "./apiResponse.type"
import { TItemCondition, TPackagingCondition } from "./listing.type"
import { TListingItem } from "./listingItem.type"
import { Product } from "./product.type"
import { TSize } from "./size.type"
import { TStripePrice } from "./stripe.type"
import { TUserDetail } from "./user.type"

export enum EStatusOffer {
  Pending = "PENDING",
  AwaitingPayment = "AWAITING_PAYMENT",
  Completed = "COMPLETED",
  Canceled = "CANCELED",
  Expired = "EXPIRED",
  Active = "ACTIVE",
  UnKnown = "UN_KNOWN",
}

export enum EDurationOffer {
  SevenDay = "7_DAY",
  FourteenDay = "14_DAY",
  ThirtyDay = "30_DAY",
}

export interface TCreateOfferPayload {
  itemId: number
  sizeId: number
  sellerListingId?: number
  amount: number
  status?: EStatusOffer
  isConsigment?: boolean
  isPreOrder?: boolean
  isNewNoDefect?: boolean
  expiredAt: EDurationOffer
}

export interface TOfferPayload {
  offerPrice: number
  expiredAt: string
}

export interface TOffer {
  id?: number
  itemId?: number
  itemName?: string
  itemSku?: string
  brandName?: string[]
  size?: TSize
  sellerListingId?: number
  amount?: TStripePrice
  status?: EStatusOffer
  acceptedAt?: string
  acceptedBy?: string
  acceptedSource?: string
  expiredAt?: string
  transactionDetailId?: number | null
  isConsignment?: boolean
  isPreOrder?: boolean
  isNewNoDefect?: boolean
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
  lowestAsk?: TStripePrice
  item?: Product
  listing?: TListingItem

  // temporary fix because missing 'n' from API response
  isConsigment?: boolean
}

export interface TOfferV2 extends Omit<TOffer, "item" | "status"> {
  item: TOfferItem
  status: string
  user: TUserDetail
  itemCondition: TItemCondition
  boxCondition: TPackagingCondition
  highestOffer: TStripePrice
}

interface TOfferItem {
  id: number
  name: string
  sku: string
  brands: string[]
  size: TSize
  image: string
}

export interface TOfferFilter extends TListParams {
  itemId?: number
  sizeId?: number[]
  status?: EStatusOffer
  sortBy?: TSort[]
  totalPages?: number
  search?: string
  categoryId?: number[]
  isNewNoDefect?: boolean
}

export interface TSellNowResponse {
  invoiceNumber: string
}

export enum EOfferType {
  Express = "EXPRESS",
  PreOrder = "PRE_ORDER",
  Standard = "STANDARD",
}
