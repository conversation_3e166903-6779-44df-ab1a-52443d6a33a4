export interface TPaginatedData<T> {
  content: T[]
  first: boolean
  last: boolean
  page: number
  pageSize: number
  sort?: TSort[]
  totalPages: number
  totalSize: number
}

export interface TListParams {
  page: number
  pageSize: number
  sort: string[]
}

export interface TSort {
  sortBy: string
  sortOrder: string
}

export interface TApiResponse<T> {
  code: number
  data?: TPaginatedData<T> | T
  message: string
  status: string
}

export interface IBaseApiResponse<T> {
  code: number
  data?: T
  message: string
  status: string
}

export interface IBasePaginatedApiResponse<T> {
  code: number
  data?: TPaginatedData<T>
  message: string
  status: string
}

export interface IAuditable {
  createdAt?: string
  createdBy?: string
  updatedAt?: string
  updatedBy?: string
}
