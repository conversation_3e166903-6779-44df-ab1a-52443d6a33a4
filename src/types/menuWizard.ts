import { Brand } from "./brand.type"
import { ELocationPageType, IRedirectionRule } from "./misc.type"
import { TStripePrice } from "./stripe.type"

export enum EMenuWizardContentType {
  Banner = "BANNER",
  Raffle = "RAFFLE",
  Collection = "COLLECTION",
}

export interface IMenuWizard {
  id: number
  title: string
  pageType: ELocationPageType
  menuDefault: boolean
  startTime: string
  endTime: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  menuWizardSections: IMenuWizardSection[]
}

export interface IMenuWizardSection {
  id: number
  menuWizardId: number
  contentType: EMenuWizardContentType
  sequence: number
  backgroundColor: string
  title: string
  description: string
  actionTitle: string
  contentId: number
  image: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  keyword: string
  sectionContent: ISectionContent | null
}

export interface ISectionContent {
  id: number
  name: string
  banner: string
  startTime: string
  endTime: string
  description: string
  termsAndConditions: string
  isActive: boolean
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
  items: ISectionContentItem[]
  imagePortrait: string
  imageLandscape: string
  redirectionRule: IRedirectionRule
  type?: string
  value?: string
  defaultSort?: string
}

export interface ISectionContentItem {
  id: number
  name: string
  skuCode: string
  description: string
  colorway: string
  gender: string
  underRetail?: boolean
  retailPrice: TStripePrice
  lowestPrice: TStripePrice
  brand: Brand
  countryId: number
  releaseDate: string
  tag: string
  weight: number
  dimension: string
  sizeChartId: number
  marketplaceLink: string
  isAutoScrape: boolean
  commissionFee: number
  isActive: boolean
  isAddOn: boolean
  isVoucherApplicable: boolean
  isReceiveSellRequest: boolean
  isCanMakeOffer: boolean
  isHotItem: boolean
  isNonPurchaseable: boolean
  images: string[]
  seoTitleIna: string
  seoKeywordIna: string[] | null
  seoDescriptionIna: string
  seoTitleEng: string
  seoKeywordEng: string[] | null
  seoDescriptionEng: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: string
}
