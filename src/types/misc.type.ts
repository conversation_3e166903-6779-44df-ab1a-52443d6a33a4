import { ReactNode } from "react"

import { TItemListing } from "./itemListing.type"

export interface BreadcrumbItem {
  name: string
  path: string
}

export interface SizeChip {
  size: string
  price: string
  leadingIcon?: ReactNode
}

export interface SizeListProps {
  list: TItemListing[]
  defaultSelected?: TItemListing
}

export type TBadgeType = "express" | "standard" | "pre-order"

export type TUpdatePriceAction = "update-to" | "update-by" | ""

export type TUpdatePriceActionType = "TO" | "BY"

export type TDropdownPlacementType =
  | "leftBottom"
  | "centerBottom"
  | "rightBottom"
  | "leftTop"
  | "centerTop"
  | "rightTop"

export type TSellingDashboardTab = "current" | "in-progress" | "selling-history"
export enum TSellingDashboardTabEnum {
  Current = "current",
  InProgress = "in-progress",
  SellingHistory = "selling-history",
}
export enum TConsignmentDashboardTabEnum {
  Pending = "pending",
  Active = "active",
  CgInProgress = "cg-in-progress",
  History = "history",
}
export type TConsignmentDashboardTab =
  | "pending"
  | "active"
  | "cg-in-progress"
  | "history"

export enum EButtonType {
  Submit = "submit",
  Button = "button",
  Reset = "reset",
}

export enum EOtpType {
  Phone = "phone",
  Email = "email",
}

export enum EOtpInputType {
  Sms = "sms",
  Email = "email",
}

export enum EOtpInputDirection {
  Next = "next",
  Previous = "previous",
}

export interface TSelectOption {
  value: string
  label: string
}

export enum ELanguage {
  Indonesia = "Indonesia",
  English = "English",
}

export enum ELanguageCode {
  Id = "id",
  En = "en",
}

export enum ECheckoutPreviewSearchKey {
  Key = "key",
}

export enum EPlatformType {
  Web = "WEB",
  App = "APP",
  Online = "ONLINE",
  All = "ALL",
}

export enum ELocationPageType {
  HomePage = "HOMEPAGE",
  Search = "SEARCH",
}

export enum ERedirectionRuleType {
  Collection = "COLLECTION",
  Item = "ITEM",
  Raffle = "RAFFLE",
  Brand = "BRAND",
  Search = "SEARCH",
  Custom = "CUSTOM",
}

export interface IRedirectionRule {
  type: ERedirectionRuleType
  keywords: string
}
